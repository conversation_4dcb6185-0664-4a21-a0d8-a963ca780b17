import {
  setupE2ETestHooks,
  expectNotice,
  executeCommand,
  expectPostFile,
  createManagedPostManager
} from '../helpers/shared-context';

import { test, expect, describe } from 'vitest';

describe("Browse and import post", () => {
  const context = setupE2ETestHooks();
  const postManager = createManagedPostManager();

  test("should import a new post", async () => {
    const testPost = await postManager().createPostFromFixture('sync-content-test.md');

    expect(testPost).toBeTruthy();
    expect(testPost.title).toBe('Sync Content Test');
    expect(testPost.slug).toBe('sync-content-test');

    await context.page.waitForTimeout(2000);

    const fileExistsBefore = await context.page.evaluate(() => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath('articles/sync-content-test.md');
      return file !== null;
    });
    expect(fileExistsBefore).toBe(false);

    await executeCommand(context, 'Browse and sync posts from Ghost');
    await context.page.waitForTimeout(3000);

    await context.page.waitForSelector('.ghost-post-suggestion', { timeout: 10000 });

    await context.page.keyboard.type('sync-content-test');
    await context.page.keyboard.press('Enter');

    await expectNotice(context, "Synced", 15000);

    // Verify the post file was created with correct content
    await expectPostFile(context, "sync-content-test", {
      title: "Sync Content Test",
      slug: "sync-content-test",
      content: /# Sync Content Test/,
      timeout: 15000
    });

    // Verify specific content sections are present
    await expectPostFile(context, "sync-content-test", {
      content: /```javascript/
    });

    await expectPostFile(context, "sync-content-test", {
      content: /return "Sync successful"/
    });

    await expectPostFile(context, "sync-content-test", {
      content: /> \[!info\]/
    });

    await expectPostFile(context, "sync-content-test", {
      content: /This is an info callout/
    });
  });
});
