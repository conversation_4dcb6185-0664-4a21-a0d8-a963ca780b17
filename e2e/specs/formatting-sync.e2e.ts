import {
  setupE2ETestHooks,
  executeCommand,
  expectNotice,
  expectPostFile,
  createManagedPostManager
} from '../helpers/shared-context';
import {
  openGhostTab,
  waitForGhostTabStatus,
  getGhostTabSyncStatus,
  syncPost
} from '../helpers/ghost-tab-helpers';
import { setupAndOpenTestPost } from '../helpers/ghost-api-helpers';

import { test, expect, describe } from 'vitest';

describe("Comprehensive Formatting Sync Test", () => {
  const context = setupE2ETestHooks();
  const postManager = createManagedPostManager();

  test("should sync comprehensive formatting from Obsidian to Ghost and back", async () => {
    const { filePath } = await setupAndOpenTestPost(
      context,
      'comprehensive-formatting-test.md'
    );

    await openGhostTab(context);
    await waitForGhostTabStatus(context.page, 'new-post');

    const syncStatus = await getGhostTabSyncStatus(context.page);
    expect(syncStatus.isNewPost).toBe(true);
    expect(syncStatus.title).toBe('Comprehensive Formatting Test');

    await syncPost(context);

    const post = await postManager().getPostBySlug('comprehensive-formatting-test');

    expect(post).toBeTruthy();

    const updateResult = await postManager().addParagraphToPost(
      post.id,
      'This paragraph was added directly in Ghost via API.'
    );

    // Wait longer for Ghost to process the update
    await context.page.waitForTimeout(3000);

    // Ensure we're on the Ghost tab first
    await context.page.click('.workspace-tab-header[aria-label*="Ghost"]');
    await context.page.waitForTimeout(1000);

    // Use quick switcher to navigate back to the file
    await executeCommand(context, 'Quick switcher: Open quick switcher');
    await context.page.keyboard.type('comprehensive-formatting-test');
    await context.page.keyboard.press('Enter');
    await context.page.waitForTimeout(2000);

    // Wait for the file to be fully loaded before opening Ghost tab
    await context.page.waitForSelector('.view-content', { timeout: 10000 });

    // Open Ghost tab and wait for it to be ready
    await openGhostTab(context);
    await context.page.waitForSelector('.ghost-sync-status-view', { timeout: 15000 });

    // Add a small delay to ensure the Ghost tab has detected any changes
    await context.page.waitForTimeout(1000);

    await syncPost(context);

    // Verify the file was updated with content from Ghost
    await expectPostFile(context, "comprehensive-formatting-test", {
      title: "Comprehensive Formatting Test",
      slug: "comprehensive-formatting-test",
      content: /This paragraph was added directly in Ghost via API\./,
      timeout: 15000
    });

    // Verify various formatting elements are preserved
    await expectPostFile(context, "comprehensive-formatting-test", {
      content: /\*\*bold text\*\*/
    });

    await expectPostFile(context, "comprehensive-formatting-test", {
      content: /\*italic text\*/
    });

    await expectPostFile(context, "comprehensive-formatting-test", {
      content: /==highlighted text==/
    });

    await expectPostFile(context, "comprehensive-formatting-test", {
      content: /`inline code`/
    });

    await expectPostFile(context, "comprehensive-formatting-test", {
      content: /~~strikethrough text~~/
    });

    await expectPostFile(context, "comprehensive-formatting-test", {
      content: /\[external link\]\(https:\/\/obsidian\.md\)/
    });

    await expectPostFile(context, "comprehensive-formatting-test", {
      content: /- \[x\] Completed task/
    });

    await expectPostFile(context, "comprehensive-formatting-test", {
      content: /- \[ \] Incomplete task/
    });

    await expectPostFile(context, "comprehensive-formatting-test", {
      content: /```javascript/
    });

    await expectPostFile(context, "comprehensive-formatting-test", {
      content: /function greetUser\(name\)/
    });

    await expectPostFile(context, "comprehensive-formatting-test", {
      content: /\| Column 1 \| Column 2 \| Column 3 \|/
    });

    await expectPostFile(context, "comprehensive-formatting-test", {
      content: /> This is a blockquote\./
    });

    await expectPostFile(context, "comprehensive-formatting-test", {
      content: /\[\^1\]: This is the first footnote\./
    });
  });
});
