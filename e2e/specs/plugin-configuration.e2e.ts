import {
  executeCommand,
  setupE2ETestHooks
} from '../helpers/shared-context';
import { test, expect } from 'vitest';

describe("Plugin Configuration", () => {
  const context = setupE2ETestHooks();

  test("should configure basic plugin settings programmatically", async () => {
    const testSettings = {
      ghostUrl: 'https://test-ghost-site.com',
      ghostAdminApiKey: 'test123:secret456',
      articlesDir: 'my-articles',
      verbose: true
    };

    await context.page.evaluate((settings) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      Object.assign(plugin.settings, settings);
      plugin.saveSettings();
    }, testSettings);

    await context.page.waitForTimeout(500);

    const pluginSettings = await context.page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      return plugin.settings;
    });

    expect(pluginSettings.ghostUrl).toBe('https://test-ghost-site.com');
    expect(pluginSettings.ghostAdminApiKey).toBe('test123:secret456');
    expect(pluginSettings.articlesDir).toBe('my-articles');
    expect(pluginSettings.verbose).toBe(true);
  });

  test("should persist plugin settings after restart", async () => {
    const testSettings = {
      ghostUrl: 'https://persistent-test.ghost.io',
      ghostAdminApiKey: 'persist123:secret789',
      articlesDir: 'persistent-articles'
    };

    await context.page.evaluate((settings) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      Object.assign(plugin.settings, settings);
      plugin.saveSettings();
    }, testSettings);

    await context.page.waitForTimeout(1000);

    await executeCommand(context, 'Reload without saving');

    await context.page.waitForFunction(() => {
      return typeof (window as any).app !== 'undefined' &&
        (window as any).app.workspace !== undefined &&
        (window as any).app.plugins.plugins['ghost-sync'] !== undefined &&
        (window as any).app.plugins.plugins['ghost-sync'].settings !== undefined;
    }, { timeout: 15000 });

    const persistedSettings = await context.page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      return plugin?.settings || null;
    });

    expect(persistedSettings).not.toBeNull();
    expect(persistedSettings?.ghostUrl).toBe(testSettings.ghostUrl);
    expect(persistedSettings?.ghostAdminApiKey).toBe(testSettings.ghostAdminApiKey);
    expect(persistedSettings?.articlesDir).toBe(testSettings.articlesDir);
  });

  test("should validate Ghost API key format programmatically", async () => {
    await context.page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      plugin.settings.ghostAdminApiKey = 'invalid-key-format';
      plugin.saveSettings();
    });

    let currentSettings = await context.page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      return plugin.settings;
    });

    expect(currentSettings.ghostAdminApiKey).toBe('invalid-key-format');

    const validKey = '1234567890abcdef1234567890:abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
    await context.page.evaluate((key) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      plugin.settings.ghostAdminApiKey = key;
      plugin.saveSettings();
    }, validKey);

    currentSettings = await context.page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      return plugin.settings;
    });

    expect(currentSettings.ghostAdminApiKey).toBe(validKey);
  });

  test("should update articles directory and affect plugin behavior", async () => {
    const customDir = 'custom-posts';

    await context.page.evaluate((dir) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      plugin.settings.articlesDir = dir;
      plugin.saveSettings();
    }, customDir);

    await context.page.waitForTimeout(500);

    const updatedSettings = await context.page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      return plugin.settings;
    });

    expect(updatedSettings.articlesDir).toBe(customDir);

    const appAdapterDir = await context.page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      return plugin.appAdapter.articlesDir;
    });

    expect(appAdapterDir).toBe(customDir);
  });
});
